//
//  AppDelegate.swift
//  Shuxiaoqi
//
//  Created by steamed_b on 2025/3/18.
//
//  主题色 #FB6C04

import UIKit
import TXLiteAVSDK_UGC
import IQKeyboardManagerSwift
import ATAuthSDK
import UserNotifications

@main
class AppDelegate: UIResponder, UIApplicationDelegate, WXApiDelegate, JPUSHRegisterDelegate {

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Override point for customization after application launch.
        
        // 启用 IQKeyboardManager
        IQKeyboardManager.shared.isEnabled = true
        IQKeyboardManager.shared.resignOnTouchOutside = true
        // 可选: 调整键盘与输入框的距离
        // IQKeyboardManager.shared.keyboardDistanceFromTextField = 10

        // 设置全局导航栏外观
        let appearance = UINavigationBar.appearance()
        appearance.isHidden = true
        appearance.setBackgroundImage(UIImage(), for: .default)
        appearance.shadowImage = UIImage()
        appearance.isTranslucent = true
        appearance.alpha = 0
        
        // 腾讯云 License 初始化（短视频 & 播放器）
        let licenceURL = "https://license.vod2.myqcloud.com/license/v2/**********_1/v_cube.license"
        let licenceKey = "00439d0788eb79c29ca1642443217418"
        TXUGCBase.setLicenceURL(licenceURL, key: licenceKey)   // UGC（录制/上传）
        TXLiveBase.setLicenceURL(licenceURL, key: licenceKey)  // 播放器 / Live
        TXLiveBase.setLogLevel(.LOGLEVEL_FATAL)
        print(TXUGCBase.getLicenseAppId() as Any)//**********
        
        //一键登录初始化
        TXCommonHandler.sharedInstance().setAuthSDKInfo("dhIF+uaiDWom2S5gEItbVa2UJTPuNpXhCiRIrY9+wqvQtRFSV9jua2ASUsDq01BHyEqKLXOyq9A00ejnWAg5SWoV8T/1gz9/11qvWISif7QF5kXNfqyDVXkNfBuiUdT+XdH9fr1A63Q7i2JgxhvbvLb3bJDUp1n5F8QF1VjflSKqx5dkzC4WHoC180Bi+JW4d1yUuNU1FTOocNJMhrY47lPkrc8rXGMtjg/g0JKPWiuQhfDLklatFzDGKbsRYJpY") { resultDic in
            print("一键登录的结果：\(resultDic)")
        }
        
        print("=== 微信SDK注册开始 ===")
        print("AppID: wx92fac135539c31c3")
        print("Universal Link: https://app.gzyoushu.com/")
        print("Bundle ID: \(Bundle.main.bundleIdentifier ?? "未知")")

        if WXApi.registerApp("wx92fac135539c31c3", universalLink: "https://app.gzyoushu.com/") {
            print("✅ 微信SDK注册成功")
            print("微信SDK版本: \(WXApi.getVersion())")
            print("微信是否安装: \(WXApi.isWXAppInstalled())")
            print("微信是否支持API: \(WXApi.isWXAppSupport())")
        } else {
            print("❌ 微信SDK注册失败")
        }
        print("=== 微信SDK注册结束 ===")
        
        //初始化Jpush
        let entity = JPUSHRegisterEntity()
        entity.types = Int(JPAuthorizationOptions.alert.rawValue | JPAuthorizationOptions.badge.rawValue | JPAuthorizationOptions.sound.rawValue)
        JPUSHService.register(forRemoteNotificationConfig: entity, delegate: self)
        // 设置JPush的AppKey
        JPUSHService.setup(withOption: launchOptions, appKey: "e59c24603c36a00ba3b4ebe8", channel: "默认通道", apsForProduction: false)
        
        // 首次进入 App（冷启动）预加载通用数据，不论是否登录
        APIManager.shared.firstEnter(
            deviceId: UIDevice.current.identifierForVendor?.uuidString ?? "",
            deviceName: UIDevice.current.name,
            deviceSystem: UIDevice.current.systemVersion,
            deviceType: 1, // 约定：iOS 传 1
            loginAddress: "" // 可根据需要获取公网 IP 或定位信息
        ) { result in
            switch result {
            case .success(let response):
                print("[firstEnter] preload success: \(response.displayMessage)")
            case .failure(let error):
                print("[firstEnter] preload failed: \(error)")
            }
        }
        
        return true
    }
    
    func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        // 检查用户活动的类型是否是 Universal Link
        if userActivity.activityType == NSUserActivityTypeBrowsingWeb {
            if let url = userActivity.webpageURL {
                // 处理打开的 URL（Universal Link）
                print("Received URL: \(url)")
                // 根据 URL 进行页面跳转或其他处理
            }
        }
        WXApi.handleOpenUniversalLink(userActivity, delegate: self)
        return true
    }

    // URL Scheme 回调
    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
        print("=== 收到URL Scheme回调 ===")
        print("URL: \(url)")
        print("Options: \(options)")

        let result = WXApi.handleOpen(url, delegate: self)
        print("WXApi处理结果: \(result)")
        return result
    }

    // 兼容老版本
    func application(_ application: UIApplication, handleOpen url: URL) -> Bool {
        print("=== 收到老版本URL回调 ===")
        print("URL: \(url)")

        let result = WXApi.handleOpen(url, delegate: self)
        print("WXApi处理结果: \(result)")
        return result
    }
    

    // MARK: UISceneSession Lifecycle
    
    // AppDelegate.swift
    func onResp(_ resp: BaseResp) {
        switch resp {
        case let auth as SendAuthResp:
            // 处理微信登录响应
            if auth.errCode == WXSuccess.rawValue, let code = auth.code {
                NotificationCenter.default.post(
                    name: .wxLoginCodeReady,
                    object: nil,
                    userInfo: ["code": code]
                )
            } else {
                print("微信登录失败: \(auth.errStr ?? "未知错误")")
            }

        case let shareResp as SendMessageToWXResp:
            // 处理微信分享响应
            print("=== 收到微信分享回调 ===")
            print("错误码: \(shareResp.errCode)")
            print("错误信息: \(shareResp.errStr ?? "无")")
            print("分享类型: \(shareResp.type)")

            if shareResp.errCode == WXSuccess.rawValue {
                print("✅ 微信分享成功")
                DispatchQueue.main.async {
                    // 可以在这里显示分享成功的提示
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let window = windowScene.windows.first {
                        let alert = UIAlertController(title: "分享成功", message: "内容已成功分享到微信", preferredStyle: .alert)
                        alert.addAction(UIAlertAction(title: "确定", style: .default))
                        window.rootViewController?.present(alert, animated: true)
                    }
                }
            } else {
                print("❌ 微信分享失败: 错误码=\(shareResp.errCode), 错误信息=\(shareResp.errStr ?? "未知错误")")

                // 解析常见错误码
                let errorMessage: String
                switch shareResp.errCode {
                case -2:
                    errorMessage = "用户取消分享"
                case -3:
                    errorMessage = "发送失败"
                case -4:
                    errorMessage = "授权拒绝"
                case -5:
                    errorMessage = "微信不支持"
                default:
                    errorMessage = shareResp.errStr ?? "分享过程中出现错误"
                }

                DispatchQueue.main.async {
                    // 显示分享失败的提示
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let window = windowScene.windows.first {
                        let alert = UIAlertController(title: "分享失败", message: errorMessage, preferredStyle: .alert)
                        alert.addAction(UIAlertAction(title: "确定", style: .default))
                        window.rootViewController?.present(alert, animated: true)
                    }
                }
            }

        case let miniProgram as WXLaunchMiniProgramResp:
            // 处理微信小程序跳转响应
            if miniProgram.errCode == WXSuccess.rawValue {
                print("微信小程序跳转成功")
                NotificationCenter.default.post(
                    name: .wxMiniProgramLaunchSuccess,
                    object: nil
                )
            } else {
                print("微信小程序跳转失败: \(miniProgram.errStr ?? "未知错误")")
                NotificationCenter.default.post(
                    name: .wxMiniProgramLaunchFailed,
                    object: nil,
                    userInfo: ["error": miniProgram.errStr ?? "未知错误"]
                )
            }

        default:
            print("收到其他微信响应: \(resp)")
        }
    }
    
    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        // Called when a new scene session is being created.
        // Use this method to select a configuration to create the new scene with.
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }
    
    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
        // Called when the user discards a scene session.
        // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
        // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
    }

    // MARK: - JPUSHRegisterDelegate
    // iOS 10及以上：前台收到推送
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (Int) -> Void) {
        let userInfo = notification.request.content.userInfo
        // 处理推送内容
        print("[JPUSH] willPresent notification: \(userInfo)")
        completionHandler(Int(UNNotificationPresentationOptions.alert.rawValue | UNNotificationPresentationOptions.sound.rawValue | UNNotificationPresentationOptions.badge.rawValue))
    }
    // iOS 10及以上：点击推送
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo
        // 处理推送点击
        print("[JPUSH] didReceive response: \(userInfo)")
        completionHandler()
    }
    // iOS 12及以上：通知设置
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, openSettingsFor notification: UNNotification) {
        print("[JPUSH] openSettingsFor notification: \(notification.request.content.userInfo)")
    }
    // 通知授权状态回调
    func jpushNotificationAuthorization(_ status: JPAuthorizationStatus, withInfo info: [AnyHashable : Any]?) {
        print("[JPUSH] Authorization status: \(status), info: \(String(describing: info))")
    }
}

extension Notification.Name {
    static let wxLoginCodeReady = Notification.Name("wxLoginCodeReady")
    static let wxMiniProgramLaunchSuccess = Notification.Name("wxMiniProgramLaunchSuccess")
    static let wxMiniProgramLaunchFailed = Notification.Name("wxMiniProgramLaunchFailed")
}
